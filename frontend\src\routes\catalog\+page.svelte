<script lang="ts">
	import { onMount } from "svelte";
	import { _ } from "$lib/i18n";
	import { getCurrentLocale, isRTL } from "$lib/i18n";

	import {
		productsStore,
		productActions,
		isLoadingProducts,
		productsError,
	} from "$lib/stores/products";
	import { cartActions } from "$lib/stores/cart";
	import { formatPriceWithCurrency } from "$lib/stores/currency";
	import type { Product } from "$lib/types/products";

	let currentLocale = "en";
	let rtl = false;
	let filteredProducts: Product[] = [];
	let selectedType = "";
	let showFeaturedOnly = false;
	let searchQuery = "";
	let sortBy = "newest";
	let currentPage = 1;
	let itemsPerPage = 12;
	let showFilters = false;

	// Reactive statements
	$: allProducts = $productsStore;
	$: isLoading = $isLoadingProducts;
	$: error = $productsError;

	// Filter and sort products
	$: {
		if (allProducts.length > 0) {
			let result = [...allProducts];

			// Apply search filter
			if (searchQuery.trim()) {
				const query = searchQuery.toLowerCase().trim();
				result = result.filter(
					(product) =>
						product.title.toLowerCase().includes(query) ||
						product.description.toLowerCase().includes(query) ||
						product.short_description
							.toLowerCase()
							.includes(query) ||
						product.tags.some((tag) =>
							tag.toLowerCase().includes(query),
						),
				);
			}

			// Apply type filter
			if (selectedType && selectedType !== "") {
				result = result.filter(
					(product) => product.type === selectedType,
				);
			}

			// Apply featured filter
			if (showFeaturedOnly) {
				result = result.filter((product) => product.is_featured);
			}

			// Apply sorting
			switch (sortBy) {
				case "newest":
					result.sort(
						(a, b) =>
							new Date(b.created_at || 0).getTime() -
							new Date(a.created_at || 0).getTime(),
					);
					break;
				case "oldest":
					result.sort(
						(a, b) =>
							new Date(a.created_at || 0).getTime() -
							new Date(b.created_at || 0).getTime(),
					);
					break;
				case "price_low":
					result.sort((a, b) => a.price - b.price);
					break;
				case "price_high":
					result.sort((a, b) => b.price - a.price);
					break;
				case "name_asc":
					result.sort((a, b) => a.title.localeCompare(b.title));
					break;
				case "name_desc":
					result.sort((a, b) => b.title.localeCompare(a.title));
					break;
			}

			filteredProducts = result;
		}
	}

	// Pagination
	$: totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
	$: paginatedProducts = filteredProducts.slice(
		(currentPage - 1) * itemsPerPage,
		currentPage * itemsPerPage,
	);

	// Reset page when filters change
	$: if (selectedType || searchQuery || showFeaturedOnly) {
		currentPage = 1;
	}

	onMount(async () => {
		currentLocale = getCurrentLocale();
		rtl = isRTL(currentLocale);

		// Load products if not already loaded
		if (allProducts.length === 0) {
			await productActions.loadProducts();
		}
	});

	async function addToCart(product: Product) {
		try {
			await cartActions.addItem(product, 1);
		} catch (error) {
			console.error("Failed to add item to cart:", error);
		}
	}

	function clearFilters() {
		selectedType = "";
		showFeaturedOnly = false;
		searchQuery = "";
		currentPage = 1;
	}

	function changePage(page: number) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
			// Scroll to top of products
			document
				.getElementById("products-grid")
				?.scrollIntoView({ behavior: "smooth" });
		}
	}
</script>

<svelte:head>
	<title>{$_("catalog.page_title")}</title>
</svelte:head>

<div class="min-h-screen" style="background: var(--color-bg-primary);">
	<!-- Header -->
	<div
		class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-5"
		style="background: var(--color-bg-primary);"
	>
		<h1
			class="font-title text-3xl md:text-4xl font-medium"
			style="color: var(--color-text-primary);"
		>
			{$_("catalog.title")}
		</h1>
	</div>

	<!-- Filters and Search -->
	{#if showFilters}
		<section class="py-8" style="background: var(--color-bg-primary);">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<!-- Search Bar -->
				<div class="mb-6">
					<div class="relative max-w-md mx-auto">
						<input
							type="text"
							bind:value={searchQuery}
							placeholder={$_("catalog.search_placeholder")}
							class="w-full px-4 py-3 pl-10 rounded-lg border font-book-text"
							style="background: var(--color-bg-primary); border-color: var(--color-border); color: var(--color-text-primary);"
						/>
						<div
							class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
						>
							<svg
								class="h-5 w-5"
								style="color: var(--color-text-accent);"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
								></path>
							</svg>
						</div>
					</div>
				</div>

				<!-- Filters -->
				<div class="flex flex-wrap gap-4 mb-6 justify-center">
					<!-- Featured Only Filter -->
					<label
						class="flex items-center space-x-2 font-book-text text-sm"
					>
						<input
							type="checkbox"
							bind:checked={showFeaturedOnly}
							class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
						/>
						<span
							class="ml-2 font-book-text text-sm"
							style="color: var(--color-text-secondary);"
							>{$_("catalog.featured_only")}</span
						>
					</label>

					<!-- Product Type Filter -->
					<div class="min-w-[180px]">
						<select
							bind:value={selectedType}
							class="w-full px-3 py-2 rounded-lg border font-book-text text-sm"
							style="background: var(--color-bg-primary); border-color: var(--color-border); color: var(--color-text-primary);"
						>
							<option value="">{$_("catalog.all_types")}</option>
							<option value="book"
								>{$_("catalog.product_types.book")}</option
							>
							<option value="chapter"
								>{$_("catalog.product_types.chapter")}</option
							>
							<option value="subscription"
								>{$_(
									"catalog.product_types.subscription",
								)}</option
							>
						</select>
					</div>

					<!-- Sort -->
					<div class="min-w-[180px]">
						<select
							bind:value={sortBy}
							class="w-full px-3 py-2 rounded-lg border font-book-text text-sm"
							style="background: var(--color-bg-primary); border-color: var(--color-border); color: var(--color-text-primary);"
						>
							<option value="newest"
								>{$_("catalog.sort.newest")}</option
							>
							<option value="oldest"
								>{$_("catalog.sort.oldest")}</option
							>
							<option value="price_low"
								>{$_("catalog.sort.price_low")}</option
							>
							<option value="price_high"
								>{$_("catalog.sort.price_high")}</option
							>
							<option value="name_asc"
								>{$_("catalog.sort.name_asc")}</option
							>
							<option value="name_desc"
								>{$_("catalog.sort.name_desc")}</option
							>
						</select>
					</div>
				</div>

				<!-- Clear Filters Button -->
				<div class="flex justify-center mb-6">
					<button
						on:click={clearFilters}
						class="px-6 py-2 rounded-lg font-book-text text-sm transition-all duration-300 hover:shadow-md"
						style="background: var(--color-bg-subtle); color: var(--color-text-accent); border: 1px solid var(--color-border);"
					>
						{$_("catalog.clear_filters")}
					</button>
				</div>
			</div>
		</section>
	{/if}
	<!-- Products Grid -->
	<section class="py-4" style="background: var(--color-bg-primary);">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<!-- Results Info -->
			<div class="flex justify-center items-center mb-6">
				<p
					class="font-book-text text-sm"
					style="color: var(--color-text-secondary);"
				>
					Showing {(currentPage - 1) * itemsPerPage + 1}-{Math.min(
						currentPage * itemsPerPage,
						filteredProducts.length,
					)} of {filteredProducts.length} results
				</p>
			</div>

			{#if isLoading}
				<div class="text-center py-12">
					<div
						class="inline-flex items-center px-4 py-2 font-book-text text-sm"
						style="color: var(--color-text-secondary);"
					>
						<div
							class="animate-spin rounded-full h-4 w-4 border-b-2 mr-3"
							style="border-color: var(--color-primary);"
						></div>
						{$_("catalog.loading")}
					</div>
				</div>
			{:else if error}
				<div class="text-center py-12">
					<p
						class="font-book-text text-base"
						style="color: var(--color-text-error);"
					>
						{$_("catalog.error")}: {error}
					</p>
				</div>
			{:else if paginatedProducts.length === 0}
				<div class="text-center py-12">
					<p
						class="font-book-text text-base"
						style="color: var(--color-text-secondary);"
					>
						{$_("catalog.no_products")}
					</p>
				</div>
			{:else}
				<!-- Products Grid -->
				<div
					id="products-grid"
					class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-16 mb-8"
				>
					{#each paginatedProducts as product (product.id)}
						<div
							class="card-book p-6 overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 items-center"
						>
							<!-- Product Image/Icon -->
							<div
								class="h-auto w-auto m-2 flex items-center justify-center relative overflow-hidden transition-transform duration-300 hover:scale-105"
								style="background: var(--color-bg-primary);"
							>
								{#if product.cover_image}
									<!-- Product has image -->
									<img
										src={product.cover_image}
										alt={product.title}
										class="w-auto h-full object-cover"
										loading="lazy"
										on:error={(e) => {
											// Hide image and show fallback icon if image fails to load
											const target =
												e.target as HTMLImageElement;
											if (target) {
												target.style.display = "none";
												const fallback =
													target.nextElementSibling as HTMLElement;
												if (fallback)
													fallback.style.display =
														"flex";
											}
										}}
									/>
									<!-- Fallback icon (hidden by default) -->
									<div
										class="absolute inset-0 flex items-center justify-center text-center"
										style="display: none;"
									>
										<div>
											{#if product.type?.toLowerCase() === "chapter"}
												<div class="text-6xl mb-2">
													📖
												</div>
											{:else if product.type?.toLowerCase() === "subscription"}
												<div class="text-6xl mb-2">
													⭐
												</div>
											{:else if product.type?.toLowerCase() === "book"}
												<div class="text-6xl mb-2">
													📚
												</div>
											{:else}
												<div class="text-6xl mb-2">
													📦
												</div>
											{/if}
											<div
												class="font-book-text text-xs uppercase tracking-wide"
												style="color: var(--color-text-accent);"
											>
												{product.type || "Product"}
											</div>
										</div>
									</div>
								{:else}
									<!-- No image - show icon -->
									<div class="text-center">
										{#if product.type?.toLowerCase() === "chapter"}
											<div class="text-6xl mb-2">📖</div>
										{:else if product.type?.toLowerCase() === "subscription"}
											<div class="text-6xl mb-2">⭐</div>
										{:else if product.type?.toLowerCase() === "book"}
											<div class="text-6xl mb-2">📚</div>
										{:else}
											<div class="text-6xl mb-2">📦</div>
										{/if}
										<div
											class="font-book-text text-xs uppercase tracking-wide"
											style="color: var(--color-text-accent);"
										>
											{product.type || "Product"}
										</div>
									</div>
								{/if}

								<!-- Product type badge -->
								<div
									class="absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-book-text uppercase tracking-wide"
									style="background: var(--color-primary); color: white; opacity: 0.9;"
								>
									{product.type || "Product"}
								</div>
							</div>

							<!-- Product Info -->
							<div class="p-6">
								<h3
									class="font-title text-2xl mb-2 font-medium"
									style="color: var(--color-text-primary); "
								>
									{product.title}
								</h3>

								<!-- Price and Actions -->
								<div class="space-y-3 flex flex-col items-center">
									<div class="text-center font-book-title text-xl font-bold mb-1">
											{product.formatted_price}
									</div>
									<button
										on:click={() => addToCart(product)}
										class="btn-primary"
									>
										{$_("catalog.add_to_cart")}
									</button>
								</div>

								<!-- Tags -->
								{#if product.tags.length > 0}
									<div class="mt-4 flex flex-wrap gap-1">
										{#each product.tags.slice(0, 3) as tag}
											<span
												class="px-2 py-1 rounded-full font-book-text text-xs"
												style="background: var(--color-bg-accent); color: var(--color-text-accent);"
											>
												{tag}
											</span>
										{/each}
									</div>
								{/if}
							</div>
						</div>
					{/each}
				</div>

				<!-- Pagination -->
				{#if totalPages > 1}
					<div class="flex justify-center items-center space-x-2">
						<button
							on:click={() => changePage(currentPage - 1)}
							disabled={currentPage === 1}
							class="px-3 py-2 rounded-lg font-book-text text-sm transition-all duration-300"
							class:opacity-50={currentPage === 1}
							style="background: var(--color-bg-subtle); color: var(--color-text-accent); border: 1px solid var(--color-border);"
						>
							{$_("catalog.previous")}
						</button>

						{#each Array.from({ length: totalPages }, (_, i) => i + 1) as page}
							{#if page === currentPage || page === 1 || page === totalPages || (page >= currentPage - 1 && page <= currentPage + 1)}
								<button
									on:click={() => changePage(page)}
									class="px-3 py-2 rounded-lg font-book-text text-sm transition-all duration-300"
									class:btn-classic={page === currentPage}
									style={page === currentPage
										? ""
										: "background: var(--color-bg-subtle); color: var(--color-text-accent); border: 1px solid var(--color-border);"}
								>
									{page}
								</button>
							{:else if page === currentPage - 2 || page === currentPage + 2}
								<span
									class="px-2 font-book-text text-sm"
									style="color: var(--color-text-accent);"
									>...</span
								>
							{/if}
						{/each}

						<button
							on:click={() => changePage(currentPage + 1)}
							disabled={currentPage === totalPages}
							class="px-3 py-2 rounded-lg font-book-text text-sm transition-all duration-300"
							class:opacity-50={currentPage === totalPages}
							style="background: var(--color-bg-subtle); color: var(--color-text-accent); border: 1px solid var(--color-border);"
						>
							{$_("catalog.next")}
						</button>
					</div>
				{/if}
			{/if}
		</div>
	</section>
</div>
