<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { authStore } from '$lib/stores/auth'
  import { _ } from '$lib/i18n'

  const dispatch = createEventDispatcher()

  // Form data
  let formData = {
    email: '',
    password: '',
    confirmPassword: '',
    first_name: '',
    last_name: '',
    phone: ''
  }

  // Form state
  let isSubmitting = false
  let errors: Record<string, string> = {}
  let showPassword = false
  let showConfirmPassword = false

  // Validation
  function validateForm() {
    errors = {}

    // Email validation
    if (!formData.email) {
      errors.email = $_('auth.email_required')
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = $_('auth.email_invalid')
    }

    // Password validation
    if (!formData.password) {
      errors.password = $_('auth.password_required')
    } else if (formData.password.length < 8) {
      errors.password = $_('auth.password_min_length')
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      errors.confirmPassword = $_('auth.confirm_password_required')
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = $_('auth.passwords_dont_match')
    }

    // First name validation
    if (!formData.first_name.trim()) {
      errors.first_name = $_('auth.first_name_required')
    }

    return Object.keys(errors).length === 0
  }

  async function handleSubmit() {
    if (!validateForm()) {
      return
    }

    isSubmitting = true

    try {
      const result = await authStore.register({
        email: formData.email.trim(),
        password: formData.password,
        first_name: formData.first_name.trim(),
        last_name: formData.last_name.trim(),
        phone: formData.phone.trim() || undefined
      })

      if (result.success) {
        dispatch('success', { customer: result.customer })
      } else {
        errors.general = result.error || $_('auth.registration_failed')
      }
    } catch (error) {
      errors.general = error instanceof Error ? error.message : $_('auth.registration_failed')
    } finally {
      isSubmitting = false
    }
  }

  function clearError(field: string) {
    if (errors[field]) {
      delete errors[field]
      errors = { ...errors }
    }
  }
</script>

<form on:submit|preventDefault={handleSubmit} class="space-y-4">
  <!-- General Error -->
  {#if errors.general}
    <div class="p-4 rounded-md" style="background: #fee2e2; border: 1px solid #fecaca;">
      <p class="font-book-text text-sm" style="color: #dc2626;">
        {errors.general}
      </p>
    </div>
  {/if}

  <!-- Email -->
  <div>
    <label for="email" class="block font-book-text text-sm font-medium mb-2" style="color: var(--color-text-primary);">
      {$_('auth.email')} *
    </label>
    <input
      id="email"
      type="email"
      bind:value={formData.email}
      on:input={() => clearError('email')}
      required
      class="w-full px-3 py-2 font-book-text border rounded-md focus:outline-none focus:ring-2 transition-colors"
      style="border-color: {errors.email ? '#dc2626' : 'var(--color-border)'}; background: var(--color-bg-primary) !important; color: var(--color-text-primary); focus:ring-color: var(--color-primary);"
      placeholder={$_('auth.email_placeholder')}
      disabled={isSubmitting}
    />
    {#if errors.email}
      <p class="mt-1 font-book-text text-sm" style="color: #dc2626;">{errors.email}</p>
    {/if}
  </div>

  <!-- First Name -->
  <div>
    <label for="first_name" class="block font-book-text text-sm font-medium mb-2" style="color: var(--color-text-primary);">
      {$_('auth.first_name')} *
    </label>
    <input
      id="first_name"
      type="text"
      bind:value={formData.first_name}
      on:input={() => clearError('first_name')}
      required
      class="w-full px-3 py-2 font-book-text border rounded-md focus:outline-none focus:ring-2 transition-colors"
      style="border-color: {errors.first_name ? '#dc2626' : 'var(--color-border)'}; background: var(--color-bg-primary); color: var(--color-text-primary); focus:ring-color: var(--color-primary);"
      placeholder={$_('auth.first_name_placeholder')}
      disabled={isSubmitting}
    />
    {#if errors.first_name}
      <p class="mt-1 font-book-text text-sm" style="color: #dc2626;">{errors.first_name}</p>
    {/if}
  </div>

  <!-- Last Name -->
  <div>
    <label for="last_name" class="block font-book-text text-sm font-medium mb-2" style="color: var(--color-text-primary);">
      {$_('auth.last_name')}
    </label>
    <input
      id="last_name"
      type="text"
      bind:value={formData.last_name}
      class="w-full px-3 py-2 font-book-text border rounded-md focus:outline-none focus:ring-2 transition-colors"
      style="border-color: var(--color-border); background: var(--color-bg-primary); color: var(--color-text-primary); focus:ring-color: var(--color-primary);"
      placeholder={$_('auth.last_name_placeholder')}
      disabled={isSubmitting}
    />
  </div>

  <!-- Phone -->
  <div>
    <label for="phone" class="block font-book-text text-sm font-medium mb-2" style="color: var(--color-text-primary);">
      {$_('auth.phone')}
    </label>
    <input
      id="phone"
      type="tel"
      bind:value={formData.phone}
      class="w-full px-3 py-2 font-book-text border rounded-md focus:outline-none focus:ring-2 transition-colors"
      style="border-color: var(--color-border); background: var(--color-bg-primary); color: var(--color-text-primary); focus:ring-color: var(--color-primary);"
      placeholder={$_('auth.phone_placeholder')}
      disabled={isSubmitting}
    />
  </div>

  <!-- Password -->
  <div>
    <label for="password" class="block font-book-text text-sm font-medium mb-2" style="color: var(--color-text-primary);">
      {$_('auth.password')} *
    </label>
    <div class="relative">
      <input
        id="password"
        type={showPassword ? 'text' : 'password'}
        bind:value={formData.password}
        on:input={() => clearError('password')}
        required
        class="w-full px-3 py-2 pr-10 font-book-text border rounded-md focus:outline-none focus:ring-2 transition-colors"
        style="border-color: {errors.password ? '#dc2626' : 'var(--color-border)'}; background: var(--color-bg-primary); color: var(--color-text-primary); focus:ring-color: var(--color-primary);"
        placeholder={$_('auth.password_placeholder')}
        disabled={isSubmitting}
      />
      <button
        type="button"
        on:click={() => showPassword = !showPassword}
        class="absolute inset-y-0 right-0 pr-3 flex items-center"
        disabled={isSubmitting}
      >
        <span class="text-sm" style="color: var(--color-text-secondary);">
          <img src="/images/eye.png" alt="Show password" class="w-auto h-6" />
        </span>
      </button>
    </div>
    {#if errors.password}
      <p class="mt-1 font-book-text text-sm" style="color: #dc2626;">{errors.password}</p>
    {/if}
  </div>

  <!-- Confirm Password -->
  <div>
    <label for="confirmPassword" class="block font-book-text text-sm font-medium mb-2" style="color: var(--color-text-primary);">
      {$_('auth.confirm_password')} *
    </label>
    <div class="relative">
      <input
        id="confirmPassword"
        type={showConfirmPassword ? 'text' : 'password'}
        bind:value={formData.confirmPassword}
        on:input={() => clearError('confirmPassword')}
        required
        class="w-full px-3 py-2 pr-10 font-book-text border rounded-md focus:outline-none focus:ring-2 transition-colors"
        style="border-color: {errors.confirmPassword ? '#dc2626' : 'var(--color-border)'}; background: var(--color-bg-primary); color: var(--color-text-primary); focus:ring-color: var(--color-primary);"
        placeholder={$_('auth.confirm_password_placeholder')}
        disabled={isSubmitting}
      />
      <button
        type="button"
        on:click={() => showConfirmPassword = !showConfirmPassword}
        class="absolute inset-y-0 right-0 pr-3 flex items-center"
        disabled={isSubmitting}
      >
        <span class="text-sm" style="color: var(--color-text-secondary);">
          <img src="/images/eye.png" alt="Show password" class="w-4 h-6" />
        </span>
      </button>
    </div>
    {#if errors.confirmPassword}
      <p class="mt-1 font-book-text text-sm" style="color: #dc2626;">{errors.confirmPassword}</p>
    {/if}
  </div>

  <!-- Submit Button -->
  <button
    type="submit"
    disabled={isSubmitting}
    class="w-full btn-primary flex items-center justify-center"
    class:opacity-50={isSubmitting}
  >
    {#if isSubmitting}
      <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
      {$_('auth.registering')}
    {:else}
      {$_('auth.create_account')}
    {/if}
  </button>

  <!-- Login Link -->
  <div class="text-center">
    <p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
      {$_('auth.already_have_account')}
      <button
        type="button"
        on:click={() => dispatch('switch-to-login')}
        class="font-medium underline hover:no-underline transition-all"
        style="color: var(--color-primary);"
        disabled={isSubmitting}
      >
        {$_('auth.sign_in')}
      </button>
    </p>
  </div>
</form>
